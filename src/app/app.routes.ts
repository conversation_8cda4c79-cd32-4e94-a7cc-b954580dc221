import { Routes } from '@angular/router';
import { HomeComponent } from './components/home/<USER>';
import { FormulaireComponent } from './components/formulaire/formulaire.component';
import { SelectingMissionComponent } from './components/selecting-mission/selecting-mission.component';
import { LoadingComponent } from './components/loading/loading.component';
import { SpectroComponent } from './components/spectro/spectro.component';
import { ScoreComponent } from './components/score/score.component';
import { ReflectionAnalyticsComponent } from './components/reflection-analytics/reflection-analytics.component';
import { TimerComponent } from './components/timer/timer.component';
import { AudioPlayerComponent } from './components/audio-player/audio-player.component';
import {StaiYComponent} from './components/questionnaires/stai-y/stai-y.component';

export const routes: Routes = [
  { path: '', redirectTo: '/formulaire', pathMatch: 'full' },
  { path: 'formulaire', component: FormulaireComponent },
  { path: 'home', component: HomeComponent },
  { path: 'selecting-mission', component: SelectingMissionComponent },
  { path: 'loading', component: LoadingComponent },
  { path: 'spectro', component: SpectroComponent },
  { path: 'score', component: ScoreComponent },
  { path: 'analytics', component: ReflectionAnalyticsComponent },
  { path: 'timer', component: TimerComponent },
  { path: 'audio_sound', component: AudioPlayerComponent },
  { path: 'stai-y', component: StaiYComponent },
  { path: '**', redirectTo: '/formulaire' }
];
