import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { NgIf, NgForOf } from '@angular/common';
import { Router } from '@angular/router';
import { LocalStorageService } from '../../../services/local-storage.service';
import { ClickstreamService } from '../../../services/clickstream.service';

interface MentalLoadQuestion {
  id: number;
  text: string;
}

interface FatigueQuestion {
  id: number;
  text: string;
}

interface SatisfactionQuestion {
  id: number;
  text: string;
}

@Component({
  selector: 'app-post-mission',
  imports: [ReactiveFormsModule, NgIf, NgForOf],
  templateUrl: './post-mission.component.html',
  styleUrl: './post-mission.component.css'
})
export class PostMissionComponent implements OnInit {
  currentPage: number = 0;
  totalPages: number = 4;

  questionnaireForms: FormGroup[] = [];

  // Mental Load Questions (7-point scale)
  mentalLoadQuestions: MentalLoadQuestion[] = [
    { id: 1, text: "Dans quelle mesure la tâche était-elle exigeante sur le plan mental ?" },
    { id: 2, text: "Quels efforts avez-vous dû fournir (mentalement) pour atteindre votre niveau de performance ?" },
    { id: 3, text: "Dans quelle mesure avez-vous trouvé la tâche complexe ?" },
    { id: 4, text: "Dans quelle mesure pensez-vous avoir atteint vos objectifs ?" },
    { id: 5, text: "Avez-vous ressenti du stress, de l'irritabilité ou de la frustration pendant l'expérience ?" },
    { id: 6, text: "Je me sens mentalement vide après cette tâche" }
  ];

  // Fatigue Questions (5-point scale)
  fatigueQuestions: FatigueQuestion[] = [
    { id: 1, text: "Je me sens physiquement épuisé après avoir accompli cette tâche." },
    { id: 2, text: "J'ai eu du mal à rester concentré pendant la tâche" },
    { id: 3, text: "J'ai l'impression d'avoir moins d'énergie que d'habitude à ce stade." },
    { id: 4, text: "Je me sens moins motivé pour continuer à travailler sur des tâches similaires." },
    { id: 5, text: "Je pense que j'aurai besoin d'un repos ou d'une pause notable pour me remettre de cette tâche." }
  ];

  // Self-Satisfaction Questions (5-point scale)
  satisfactionQuestions: SatisfactionQuestion[] = [
    { id: 1, text: "Je suis satisfait de la manière dont j'ai réalisé la tâche" },
    { id: 2, text: "Je pense avoir bien accompli la mission" },
    { id: 3, text: "Je pense avoir obtenu un bon score" }
  ];

  // Scale options
  mentalLoadOptions = [
    { value: 1, label: '1' },
    { value: 2, label: '2' },
    { value: 3, label: '3' },
    { value: 4, label: '4' },
    { value: 5, label: '5' },
    { value: 6, label: '6' },
    { value: 7, label: '7' }
  ];

  fatigueOptions = [
    { value: 1, label: 'Fortement en désaccord' },
    { value: 2, label: 'En désaccord' },
    { value: 3, label: 'Ni d\'accord ni en désaccord' },
    { value: 4, label: 'D\'accord' },
    { value: 5, label: 'Tout à fait d\'accord' }
  ];

  satisfactionOptions = [
    { value: 1, label: 'Fortement en désaccord' },
    { value: 2, label: 'En désaccord' },
    { value: 3, label: 'Ni d\'accord ni en désaccord' },
    { value: 4, label: 'D\'accord' },
    { value: 5, label: 'Tout à fait d\'accord' }
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private localStorage: LocalStorageService,
    private clickstreamService: ClickstreamService
  ) {}

  ngOnInit() {
    this.initializeForms();
    // Track questionnaire start
    this.clickstreamService.trackEvent('questionnaire_started', {
      elementId: 'post-mission-questionnaire',
      metadata: {
        userId: this.localStorage.getItem('uniqueId'),
        timestamp: new Date().toISOString()
      }
    });
  }

  initializeForms() {
    // Welcome page form (no questions)
    this.questionnaireForms[0] = this.fb.group({});

    // Mental Load form
    const mentalLoadControls = this.mentalLoadQuestions.reduce((group, q) => {
      group['q' + q.id] = [null, Validators.required];
      return group;
    }, {} as any);
    this.questionnaireForms[1] = this.fb.group(mentalLoadControls);

    // Fatigue form
    const fatigueControls = this.fatigueQuestions.reduce((group, q) => {
      group['q' + q.id] = [null, Validators.required];
      return group;
    }, {} as any);
    this.questionnaireForms[2] = this.fb.group(fatigueControls);

    // Satisfaction form
    const satisfactionControls = this.satisfactionQuestions.reduce((group, q) => {
      group['q' + q.id] = [null, Validators.required];
      return group;
    }, {} as any);
    this.questionnaireForms[3] = this.fb.group(satisfactionControls);
  }

  nextPage() {
    if (this.currentPage < this.totalPages - 1) {
      if (this.currentPage === 0 || this.isCurrentPageValid()) {
        // Track page navigation
        this.clickstreamService.trackEvent('questionnaire_page_next', {
          elementId: 'next-button',
          metadata: {
            fromPage: this.currentPage,
            toPage: this.currentPage + 1,
            pageTitle: this.getPageTitle()
          }
        });
        this.currentPage++;
      }
    }
  }

  previousPage() {
    if (this.currentPage > 0) {
      // Track page navigation
      this.clickstreamService.trackEvent('questionnaire_page_previous', {
        elementId: 'previous-button',
        metadata: {
          fromPage: this.currentPage,
          toPage: this.currentPage - 1,
          pageTitle: this.getPageTitle()
        }
      });
      this.currentPage--;
    }
  }

  isCurrentPageValid(): boolean {
    if (this.currentPage === 0) return true; // Welcome page
    return this.questionnaireForms[this.currentPage].valid;
  }

  canProceed(): boolean {
    return this.currentPage === 0 || this.isCurrentPageValid();
  }

  finishQuestionnaire() {
    if (this.areAllFormsValid()) {
      // Track questionnaire completion
      this.clickstreamService.trackEvent('questionnaire_completed', {
        elementId: 'finish-button',
        metadata: {
          userId: this.localStorage.getItem('uniqueId'),
          completionTime: new Date().toISOString(),
          totalPages: this.totalPages
        }
      });

      this.saveQuestionnaireData();
      // Navigate to next component or show completion message
      this.router.navigate(['/score']); // Adjust route as needed
    }
  }

  areAllFormsValid(): boolean {
    return this.questionnaireForms.slice(1).every(form => form.valid);
  }

  saveQuestionnaireData() {
    const questionnaireData = {
      timestamp: new Date().toISOString(),
      userId: this.localStorage.getItem('uniqueId'),
      mentalLoad: this.questionnaireForms[1].value,
      fatigue: this.questionnaireForms[2].value,
      satisfaction: this.questionnaireForms[3].value
    };

    // Save to localStorage
    this.localStorage.setItem('post_mission_questionnaire', JSON.stringify(questionnaireData));

    // TODO: Send to backend API if needed
    console.log('Questionnaire data saved:', questionnaireData);
  }

  getPageTitle(): string {
    switch (this.currentPage) {
      case 0: return 'Questionnaire';
      case 1: return 'Charge mentale';
      case 2: return 'Fatigue';
      case 3: return 'Satisfaction de soi';
      default: return 'Questionnaire';
    }
  }
}
