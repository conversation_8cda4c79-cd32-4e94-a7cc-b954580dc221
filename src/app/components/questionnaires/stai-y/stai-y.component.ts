import { Component } from '@angular/core';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {NgForOf} from '@angular/common';

interface Question {
  id: number;
  text: string;
  positive: boolean; // true if anxiety-absent (reverse scored)
}


@Component({
  selector: 'app-stai-y',
  imports: [
    NgForOf,
    ReactiveFormsModule
  ],
  templateUrl: './stai-y.component.html',
  styleUrl: './stai-y.component.css'
})
export class StaiYComponent {
  form: FormGroup;
  options = [
    { value: 1, label: 'Pas du tout' },
    { value: 2, label: 'Un peu' },
    { value: 3, label: 'Modérément' },
    { value: 4, label: 'Beaucoup' }
  ];

  questions: Question[] = [
    { id: 1, text: 'Je me sens calme.', positive: true },
    { id: 2, text: 'Je me sens en sécurité.', positive: true },
    { id: 3, text: 'Je suis tendu(e).', positive: false },
    { id: 4, text: 'Je me sens surmené(e).', positive: false },
    { id: 5, text: 'Je me sens tranquille.', positive: true },
    { id: 6, text: 'Je me sens ému(e), bouleversé(e).', positive: false },
    { id: 7, text: 'Je m\'inquiète à l\'idée de malheurs possibles.', positive: false },
    { id: 8, text: 'Je me sens comblé(e).', positive: true },
    { id: 9, text: 'Je me sens effrayé(e).', positive: false },
    { id: 10, text: 'Je me sens bien, à l’aise.', positive: true },
    { id: 11, text: 'Je me sens sûr(e) de moi.', positive: true },
    { id: 12, text: 'Je me sens nerveux(e).', positive: false },
    { id: 13, text: 'Je suis agité(e).', positive: false },
    { id: 14, text: 'Je me sens indécis(e).', positive: false },
    { id: 15, text: 'Je suis détendu(e).', positive: true },
    { id: 16, text: 'Je me sens satisfait(e).', positive: true },
    { id: 17, text: 'Je suis inquiet(e).', positive: false },
    { id: 18, text: 'Je me sens troublé(e).', positive: false },
    { id: 19, text: 'Je sens que j’ai les nerfs solides.', positive: true },
    { id: 20, text: 'Je me sens dans de bonnes dispositions.', positive: true }
  ];

  constructor(private fb: FormBuilder) {
    const qControls = this.questions.reduce((group, q) => {
      group['q' + q.id] = [null, Validators.required];
      return group;
    }, {} as any);

    this.form = this.fb.group({
      name: [''],
      gender: ['', Validators.required],
      dob: ['', Validators.required],
      testDate: [this.today(), Validators.required],
      logoUrl: [''],
      ...qControls
    });

    this.form.valueChanges.subscribe(() => this.updateScore());
  }

  today(): string {
    const d = new Date();
    return d.toISOString().substring(0, 10);
  }

  get score(): number {
    if (!this.form.valid) return 0;
    let total = 0;
    for (const q of this.questions) {
      const raw = this.form.get('q' + q.id)!.value as number;
      total += q.positive ? (5 - raw) : raw;
    }
    return total;
  }

  updateScore() {
    // Trigger change detection for score display
  }

  reset() {
    this.form.reset({ testDate: this.today() });
  }

}
